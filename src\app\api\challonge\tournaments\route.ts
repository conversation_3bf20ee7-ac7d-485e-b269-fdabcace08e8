import { NextRequest, NextResponse } from 'next/server';
import { challongeAPI } from '@/lib/challonge';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, tournament_type } = body;

    if (!name || !tournament_type) {
      return NextResponse.json(
        { error: 'Name and tournament type are required' },
        { status: 400 }
      );
    }

    const tournament = await challongeAPI.createTournament({
      name,
      tournament_type
    });

    return NextResponse.json({ tournament });
  } catch (error) {
    console.error('Error creating tournament:', error);
    return NextResponse.json(
      { error: 'Failed to create tournament' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const tournaments = await challongeAPI.getTournaments();
    return NextResponse.json({ tournaments });
  } catch (error) {
    console.error('Error fetching tournaments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tournaments' },
      { status: 500 }
    );
  }
}