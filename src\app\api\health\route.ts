import { NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { validateEnvironmentVariables } from '@/lib/env-validation'

export async function GET() {
  try {
    // Check environment variables
    const mongoUri = process.env.MONGODB_URI
    const accessPassword = process.env.ACCESS_PASSWORD

    if (!mongoUri) {
      return NextResponse.json({
        status: 'error',
        message: 'MONGODB_URI environment variable is missing',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    if (!accessPassword) {
      return NextResponse.json({
        status: 'error',
        message: 'ACCESS_PASSWORD environment variable is missing',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    // Test database connection with timeout
    const dbPromise = getDatabase()
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Database connection timeout after 8 seconds')), 8000)
    })

    const db = await Promise.race([dbPromise, timeoutPromise]) as any

    let dbResult: { collections: string[], mapCount: number }

    try {
      // Quick ping test first
      await db.admin().ping()

      // Try to get collections with timeout
      const collectionsPromise = db.listCollections().toArray()
      const collectionsTimeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Collections query timeout')), 3000)
      })

      const collections = await Promise.race([collectionsPromise, collectionsTimeout]) as any[]

      // Quick count test
      const mapsCollection = db.collection('maps')
      const countPromise = mapsCollection.countDocuments()
      const countTimeout = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Count query timeout')), 2000)
      })

      const mapCount = await Promise.race([countPromise, countTimeout]) as number
      console.log('Maps count:', mapCount)

      dbResult = { collections: collections.map(c => c.name), mapCount }
    } catch (operationError) {
      console.warn('Database operations failed, but connection succeeded:', operationError)
      dbResult = { collections: ['connection-ok-but-operations-failed'], mapCount: -1 }
    }

    return NextResponse.json({
      status: 'healthy',
      message: 'All systems operational',
      environment: {
        mongoUri: mongoUri ? 'configured' : 'missing',
        accessPassword: accessPassword ? 'configured' : 'missing',
        nodeEnv: process.env.NODE_ENV,
        netlify: process.env.NETLIFY || 'false',
        deployUrl: process.env.DEPLOY_URL || 'not-set',
        collections: dbResult.collections,
        mapCount: dbResult.mapCount
      },
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'error',
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
