'use client';

import { useState } from 'react';
import TournamentCreator from '@/components/tournament/TournamentCreator';
import TournamentBracket from '@/components/tournament/TournamentBracket';

export default function TournamentPage() {
  const [tournamentId, setTournamentId] = useState<string | null>(null);

  const handleTournamentCreated = (id: string) => {
    setTournamentId(id);
  };

  const handleBackToCreator = () => {
    setTournamentId(null);
  };

  return (
    <div className="container mx-auto py-8 px-4 bg-slate-900 min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-2 text-white">KDF Tournament</h1>
        <p className="text-center text-gray-300">Create and manage King of Fighters tournaments</p>
      </div>

      {!tournamentId ? (
        <TournamentCreator onTournamentCreated={handleTournamentCreated} />
      ) : (
        <div className="space-y-4">
          <div className="flex justify-center">
            <button
              onClick={handleBackToCreator}
              className="text-blue-400 hover:text-blue-300 underline"
            >
              ← Create New Tournament
            </button>
          </div>
          <TournamentBracket tournamentId={tournamentId} />
        </div>
      )}
    </div>
  );
}