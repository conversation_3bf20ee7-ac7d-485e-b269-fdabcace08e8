'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

export function Navbar() {
  const pathname = usePathname()

  return (
    <nav className="sticky top-0 z-50 w-full border-b border-slate-700 bg-slate-900">
      <div className="container mx-auto flex h-16 items-center px-4">
        <div className="mr-8">
          <Link href="/" className="flex items-center text-xl font-bold text-white hover:text-slate-200 transition-colors">
            CarX Hub
          </Link>
        </div>
        <div className="flex flex-1 items-center space-x-4">
          <Link
            href="/maps"
            className={cn(
              'text-sm font-medium transition-colors hover:text-white',
              pathname === '/maps'
                ? 'text-white'
                : 'text-slate-400'
            )}
          >
            Maps
          </Link>
          <Link
            href="/cars"
            className={cn(
              'text-sm font-medium transition-colors hover:text-white',
              pathname === '/cars'
                ? 'text-white'
                : 'text-slate-400'
            )}
          >
            Cars
          </Link>
          <Link
            href="/calculator"
            className={cn(
              'text-sm font-medium transition-colors hover:text-white',
              pathname === '/calculator'
                ? 'text-white'
                : 'text-slate-400'
            )}
          >
            Calculator
          </Link>
          <Link
            href="/tournament"
            className={cn(
              'text-sm font-medium transition-colors hover:text-white',
              pathname === '/tournament'
                ? 'text-white'
                : 'text-slate-400'
            )}
          >
            KDF Tournament
          </Link>

          <Link
            href="/roulette"
            className={cn(
              'text-sm font-medium transition-colors hover:text-white',
              pathname === '/roulette'
                ? 'text-white'
                : 'text-slate-400'
            )}
          >
            KDF Roulette
          </Link>
        </div>
      </div>
    </nav>
  )
}