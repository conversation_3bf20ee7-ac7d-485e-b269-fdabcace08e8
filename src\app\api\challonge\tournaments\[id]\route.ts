import { NextRequest, NextResponse } from 'next/server';
import { challongeAPI } from '@/lib/challonge';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const tournament = await challongeAPI.getTournament(params.id);
    return NextResponse.json({ tournament });
  } catch (error) {
    console.error('Error fetching tournament:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tournament' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { action } = body;

    let result;
    switch (action) {
      case 'start':
        result = await challongeAPI.startTournament(params.id);
        break;
      case 'finalize':
        result = await challongeAPI.finalizeTournament(params.id);
        break;
      case 'reset':
        result = await challongeAPI.resetTournament(params.id);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({ tournament: result });
  } catch (error) {
    console.error('Error updating tournament:', error);
    return NextResponse.json(
      { error: 'Failed to update tournament' },
      { status: 500 }
    );
  }
}