'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tournament, Participant, Match } from '@/lib/challonge';

interface TournamentBracketProps {
  tournamentId: string;
}

export default function TournamentBracket({ tournamentId }: TournamentBracketProps) {
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTournamentData();
  }, [tournamentId]);

  const fetchTournamentData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [tournamentRes, participantsRes, matchesRes] = await Promise.all([
        fetch(`/api/challonge/tournaments/${tournamentId}`),
        fetch(`/api/challonge/participants?tournamentId=${tournamentId}`),
        fetch(`/api/challonge/matches?tournamentId=${tournamentId}`)
      ]);

      if (!tournamentRes.ok || !participantsRes.ok || !matchesRes.ok) {
        throw new Error('Failed to fetch tournament data');
      }

      const tournamentData = await tournamentRes.json();
      const participantsData = await participantsRes.json();
      const matchesData = await matchesRes.json();

      setTournament(tournamentData.tournament);
      setParticipants(participantsData.participants);
      setMatches(matchesData.matches);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleTournamentAction = async (action: string) => {
    try {
      const response = await fetch(`/api/challonge/tournaments/${tournamentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} tournament`);
      }

      fetchTournamentData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleMatchUpdate = async (matchId: string, winnerId: string, scores: string) => {
    try {
      const response = await fetch('/api/challonge/matches', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tournamentId,
          matchId,
          winner_id: winnerId,
          scores_csv: scores
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update match');
      }

      fetchTournamentData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const getParticipantName = (participantId: string | null): string => {
    if (!participantId) return 'TBD';
    const participant = participants.find(p => p.id === participantId);
    return participant?.name || 'Unknown';
  };

  const organizeMatchesByRound = (matches: Match[]) => {
    const rounds: { [key: number]: Match[] } = {};
    
    matches.forEach(match => {
      if (!rounds[match.round]) {
        rounds[match.round] = [];
      }
      rounds[match.round].push(match);
    });

    return rounds;
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-white">Loading tournament...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-red-400">Error: {error}</div>
          <Button onClick={fetchTournamentData} className="mt-4 bg-blue-600 hover:bg-blue-700">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!tournament) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-white">Tournament not found</div>
        </CardContent>
      </Card>
    );
  }

  const rounds = organizeMatchesByRound(matches);
  const roundNumbers = Object.keys(rounds).map(Number).sort((a, b) => a - b);

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-white">{tournament.name}</CardTitle>
          <div className="text-sm text-gray-300">
            <p>Type: {tournament.tournament_type}</p>
            <p>State: {tournament.state}</p>
            <p>Participants: {tournament.participants_count}</p>
            {tournament.game_name && <p>Game: {tournament.game_name}</p>}
            {tournament.description && <p>Description: {tournament.description}</p>}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            {tournament.state === 'pending' && (
              <Button onClick={() => handleTournamentAction('start')} className="bg-green-600 hover:bg-green-700">
                Start Tournament
              </Button>
            )}
            {tournament.state === 'underway' && (
              <Button onClick={() => handleTournamentAction('finalize')} className="bg-blue-600 hover:bg-blue-700">
                Finalize Tournament
              </Button>
            )}
            {tournament.state !== 'pending' && (
              <Button 
                onClick={() => handleTournamentAction('reset')}
                variant="outline"
                className="border-slate-500 text-white hover:bg-slate-700"
              >
                Reset Tournament
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {participants.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Participants</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {participants.map(participant => (
                <div key={participant.id} className="p-3 border border-slate-600 rounded-lg bg-slate-700">
                  <div className="font-semibold text-white">{participant.name}</div>
                  <div className="text-sm text-gray-300">
                    Seed: {participant.seed}
                    {participant.final_rank && ` | Rank: ${participant.final_rank}`}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {matches.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Bracket</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {roundNumbers.map(roundNumber => (
                <div key={roundNumber} className="space-y-2">
                  <h3 className="font-semibold text-lg text-white">
                    Round {roundNumber}
                    {roundNumber === Math.max(...roundNumbers) && rounds[roundNumber].length === 1 && ' (Final)'}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {rounds[roundNumber].map(match => (
                      <div key={match.id} className="border border-slate-600 rounded-lg p-4 bg-slate-700">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className={`font-medium ${match.winner_id === match.player1_id ? 'text-green-400' : 'text-white'}`}>
                              {getParticipantName(match.player1_id)}
                            </span>
                            <span className="text-sm text-gray-400">vs</span>
                            <span className={`font-medium ${match.winner_id === match.player2_id ? 'text-green-400' : 'text-white'}`}>
                              {getParticipantName(match.player2_id)}
                            </span>
                          </div>
                          
                          {match.scores_csv && (
                            <div className="text-sm text-gray-300">
                              Score: {match.scores_csv}
                            </div>
                          )}
                          
                          <div className="text-xs text-gray-400">
                            Status: {match.state}
                            {match.completed_at && (
                              <span> | Completed: {new Date(match.completed_at).toLocaleDateString()}</span>
                            )}
                          </div>
                          
                          {match.state === 'open' && match.player1_id && match.player2_id && (
                            <div className="space-y-2 pt-2">
                              <div className="text-sm font-medium text-white">Report Result:</div>
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleMatchUpdate(match.id, match.player1_id!, '2-0')}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  {getParticipantName(match.player1_id)} Wins
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => handleMatchUpdate(match.id, match.player2_id!, '0-2')}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  {getParticipantName(match.player2_id)} Wins
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}