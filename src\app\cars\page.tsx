'use client'

import { useState, useEffect } from 'react'
import { CarCard } from '@/components/CarCard'
import { CarResponse } from '@/types/database'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type SortField = 'name' | 'weight' | 'totalDownforce'
type SortOrder = 'asc' | 'desc'

type FilterRanges = {
  weight: [number, number]
  frontDownforce: [number, number]
  rearDownforce: [number, number]
  cxRatio: [number, number]
}

export default function CarsPage() {
  const [carsData, setCarsData] = useState<CarResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [ranges, setRanges] = useState<{
    weight: [number, number]
    frontDownforce: [number, number]
    rearDownforce: [number, number]
    cxRatio: [number, number]
  }>({
    weight: [0, 0],
    frontDownforce: [0, 0],
    rearDownforce: [0, 0],
    cxRatio: [0, 0]
  })

  const [searchQuery, setSearchQuery] = useState('')
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc')
  const [filters, setFilters] = useState<FilterRanges>({
    weight: [0, 0],
    frontDownforce: [0, 0],
    rearDownforce: [0, 0],
    cxRatio: [0, 0]
  })

  // Fetch cars data from API
  useEffect(() => {
    async function fetchCars() {
      try {
        setLoading(true)
        const response = await fetch('/api/cars')
        if (!response.ok) {
          throw new Error('Failed to fetch cars')
        }
        const cars: CarResponse[] = await response.json()
        setCarsData(cars)

        // Calculate ranges efficiently in a single iteration
        const newRanges = cars.reduce((acc, car) => {
          const cxRatioValue = parseFloat(car.cxRatio)
          
          return {
            weight: [
              Math.min(acc.weight[0], car.weight),
              Math.max(acc.weight[1], car.weight)
            ] as [number, number],
            frontDownforce: [
              Math.min(acc.frontDownforce[0], car.frontDownforce),
              Math.max(acc.frontDownforce[1], car.frontDownforce)
            ] as [number, number],
            rearDownforce: [
              Math.min(acc.rearDownforce[0], car.rearDownforce),
              Math.max(acc.rearDownforce[1], car.rearDownforce)
            ] as [number, number],
            cxRatio: [
              Math.min(acc.cxRatio[0], cxRatioValue),
              Math.max(acc.cxRatio[1], cxRatioValue)
            ] as [number, number]
          }
        }, {
          weight: [Infinity, -Infinity] as [number, number],
          frontDownforce: [Infinity, -Infinity] as [number, number],
          rearDownforce: [Infinity, -Infinity] as [number, number],
          cxRatio: [Infinity, -Infinity] as [number, number]
        })

        setRanges(newRanges)
        setFilters(newRanges)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchCars()
  }, [])

  const filteredCars = carsData.filter(car => {
    const matchesSearch = car.carName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesWeight = car.weight >= filters.weight[0] && car.weight <= filters.weight[1]
    const matchesFrontDownforce = car.frontDownforce >= filters.frontDownforce[0] && car.frontDownforce <= filters.frontDownforce[1]
    const matchesRearDownforce = car.rearDownforce >= filters.rearDownforce[0] && car.rearDownforce <= filters.rearDownforce[1]
    const cxRatioValue = parseFloat(car.cxRatio)
    const matchesCxRatio = cxRatioValue >= filters.cxRatio[0] && cxRatioValue <= filters.cxRatio[1]

    return matchesSearch && matchesWeight && matchesFrontDownforce && matchesRearDownforce && matchesCxRatio
  }).sort((a, b) => {
    let comparison = 0

    switch (sortField) {
      case 'name':
        comparison = a.carName.localeCompare(b.carName)
        break
      case 'weight':
        comparison = a.weight - b.weight
        break
      case 'totalDownforce':
        const totalDownforceA = a.frontDownforce + a.rearDownforce
        const totalDownforceB = b.frontDownforce + b.rearDownforce
        comparison = totalDownforceA - totalDownforceB
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  const handleSliderChange = (key: keyof FilterRanges) => (value: number[]) => {
    setFilters(prev => ({ ...prev, [key]: value as [number, number] }))
  }

  const resetFilters = () => {
    setFilters({
      weight: [ranges.weight[0], ranges.weight[1]],
      frontDownforce: [ranges.frontDownforce[0], ranges.frontDownforce[1]],
      rearDownforce: [ranges.rearDownforce[0], ranges.rearDownforce[1]],
      cxRatio: [ranges.cxRatio[0], ranges.cxRatio[1]]
    })
    setSearchQuery('')
    setSortField('name')
    setSortOrder('asc')
  }

  if (loading) {
    return (
      <main className="min-h-screen">
        <div className="container mx-auto px-4 py-12">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-900 dark:border-white mx-auto mb-4"></div>
              <p className="text-slate-600 dark:text-slate-400">Loading cars...</p>
            </div>
          </div>
        </div>
      </main>
    )
  }

  if (error) {
    return (
      <main className="min-h-screen">
        <div className="container mx-auto px-4 py-12">
          <div className="flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400 mb-4">Error: {error}</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </main>
    )
  }

  return (
    <main className="min-h-screen">
      <div className="container mx-auto">
        <div className="flex gap-6 py-12">
          {/* Left Sidebar Filters */}
          <div className="w-64 shrink-0">
            <div className="sticky top-6 space-y-6 rounded-lg border border-slate-200 bg-white p-4 dark:border-slate-700 dark:bg-slate-800">
              <div>
                <h2 className="mb-4 text-lg font-semibold text-slate-900 dark:text-white">Filters</h2>
                <input
                  type="text"
                  placeholder="Search cars..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-slate-200 px-3 py-1.5 text-sm focus:border-slate-500 focus:outline-none dark:border-slate-600 dark:bg-slate-700 dark:text-white dark:placeholder-slate-400"
                />
              </div>

              <div className="space-y-4">
                <div>
                  <label className="text-xs font-medium text-slate-900 dark:text-white">
                    Weight ({filters.weight[0].toFixed(0)} - {filters.weight[1].toFixed(0)} kg)
                  </label>
                  <Slider
                    min={ranges.weight[0]}
                    max={ranges.weight[1]}
                    step={10}
                    value={[filters.weight[0], filters.weight[1]]}
                    onValueChange={handleSliderChange('weight')}
                    className="mt-2"
                  />
                </div>

                <div>
                  <label className="text-xs font-medium text-slate-900 dark:text-white">
                    Front DF ({filters.frontDownforce[0].toFixed(1)} - {filters.frontDownforce[1].toFixed(1)})
                  </label>
                  <Slider
                    min={ranges.frontDownforce[0]}
                    max={ranges.frontDownforce[1]}
                    step={0.1}
                    value={[filters.frontDownforce[0], filters.frontDownforce[1]]}
                    onValueChange={handleSliderChange('frontDownforce')}
                    className="mt-2"
                  />
                </div>

                <div>
                  <label className="text-xs font-medium text-slate-900 dark:text-white">
                    Rear DF ({filters.rearDownforce[0].toFixed(1)} - {filters.rearDownforce[1].toFixed(1)})
                  </label>
                  <Slider
                    min={ranges.rearDownforce[0]}
                    max={ranges.rearDownforce[1]}
                    step={0.1}
                    value={[filters.rearDownforce[0], filters.rearDownforce[1]]}
                    onValueChange={handleSliderChange('rearDownforce')}
                    className="mt-2"
                  />
                </div>

                <div>
                  <label className="text-xs font-medium text-slate-900 dark:text-white">
                    CX Ratio ({filters.cxRatio[0].toFixed(2)} - {filters.cxRatio[1].toFixed(2)})
                  </label>
                  <Slider
                    min={ranges.cxRatio[0]}
                    max={ranges.cxRatio[1]}
                    step={0.01}
                    value={[filters.cxRatio[0], filters.cxRatio[1]]}
                    onValueChange={handleSliderChange('cxRatio')}
                    className="mt-2"
                  />
                </div>

                <Button
                  variant="outline"
                  onClick={resetFilters}
                  className="mt-2 w-full text-sm dark:border-slate-600 dark:text-white"
                >
                  Reset Filters
                </Button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
                CarX Drift Racing Cars
              </h1>
              <p className="mt-2 text-slate-600 dark:text-slate-400">
                Browse detailed specifications for all cars available in the game.
              </p>
              
              {/* Sorting Controls */}
              <div className="mt-6 flex gap-4">
                <div className="w-48">
                  <Select value={sortField} onValueChange={(value: SortField) => setSortField(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="weight">Weight</SelectItem>
                      <SelectItem value="totalDownforce">Total Downforce</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-48">
                  <Select value={sortOrder} onValueChange={(value: SortOrder) => setSortOrder(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Order" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">Lowest to Highest</SelectItem>
                      <SelectItem value="desc">Highest to Lowest</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3">
              {filteredCars.map((car, index) => (
                <CarCard key={index} car={car} allCars={carsData} />
              ))}
            </div>

            <div className="mt-8 text-center text-sm text-slate-500 dark:text-slate-400">
              Showing {filteredCars.length} of {carsData.length} cars
            </div>
          </div>
        </div>
      </div>
    </main>
  )
} 