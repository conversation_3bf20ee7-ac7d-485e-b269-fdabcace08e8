'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { calculateSuspension } from '@/lib/calculateSuspension';
import { SuspensionResults } from '@/types/suspension';

const CalculatorForm: React.FC = () => {
  const [totalWeight, setTotalWeight] = useState('');
  const [frontWeightPercent, setFrontWeightPercent] = useState('');
  const [results, setResults] = useState<SuspensionResults | null>(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Input validation
    if (!totalWeight || !frontWeightPercent) {
      setError('Please enter both vehicle weight and front weight percentage');
      return;
    }
    
    const totalWeightNum = parseFloat(totalWeight);
    const frontWeightPercentNum = parseFloat(frontWeightPercent);
    
    // Range validation
    if (totalWeightNum <= 0) {
      setError('Vehicle weight must be greater than 0');
      return;
    }
    
    if (frontWeightPercentNum <= 0 || frontWeightPercentNum > 100) {
      setError('Front weight percentage must be between 0 and 100 (inclusive)');
      return;
    }
    
    setError('');
    
    try {
      // Calculate locally rather than sending to API
      const suspensionResults = calculateSuspension({
        totalWeight: totalWeightNum,
        frontWeightPercent: frontWeightPercentNum
      });
      
      setResults(suspensionResults);
    } catch (error) {
      setError('Calculation error occurred');
    }
  };

  const formatNumber = (num: number): string => {
    return num.toFixed(2);
  };

  return (
    <div className="p-6 bg-gray-800 rounded shadow-md max-w-md mx-auto mt-10 text-white transition-transform transform hover:scale-105">
      <h2 className="text-2xl mb-4 text-center">Suspension Calculator</h2>
      <p className="text-gray-300 mb-4 text-sm">
        Enter vehicle weight and front weight distribution to calculate optimal suspension parameters
      </p>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="totalWeight" className="block text-sm font-medium text-gray-300 mb-1">
            Vehicle Weight (kg)
          </label>
          <input
            id="totalWeight"
            type="number"
            value={totalWeight}
            onChange={(e) => setTotalWeight(e.target.value)}
            className="w-full p-2 border border-gray-600 rounded bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            placeholder="e.g., 1200"
            aria-label="Vehicle Weight"
            min="0"
            step="1"
          />
        </div>
        
        <div>
          <label htmlFor="frontWeightPercent" className="block text-sm font-medium text-gray-300 mb-1">
            Front Weight Distribution (%)
          </label>
          <input
            id="frontWeightPercent"
            type="number"
            value={frontWeightPercent}
            onChange={(e) => setFrontWeightPercent(e.target.value)}
            className="w-full p-2 border border-gray-600 rounded bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            placeholder="e.g., 49.5"
            aria-label="Front Weight Percentage"
            min="0"
            max="100"
            step="0.1"
          />
        </div>
        
        {error && <p className="text-red-500 text-center" role="alert">{error}</p>}
        
        <Button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          Calculate Parameters
        </Button>
      </form>
      
      {results && (
        <div className="mt-6">
          <h3 className="text-xl mb-2">Calculated Parameters</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="text-lg text-blue-400 mb-2">Front Axle</h4>
              <div className="bg-gray-700 p-4 rounded space-y-2">
                {Object.entries(results["Front Axle"]).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-gray-300">{key}:</span>
                    <span className="font-mono">{formatNumber(value)}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="text-lg text-blue-400 mb-2">Rear Axle</h4>
              <div className="bg-gray-700 p-4 rounded space-y-2">
                {Object.entries(results["Rear Axle"]).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-gray-300">{key}:</span>
                    <span className="font-mono">{formatNumber(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CalculatorForm;