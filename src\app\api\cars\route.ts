import { NextRequest, NextResponse } from 'next/server'
import { getDatabase } from '@/lib/mongodb'
import { CarDocument, CarResponse, COLLECTIONS } from '@/types/database'

// GET /api/cars - Fetch all cars
export async function GET() {
  try {
    const db = await getDatabase()
    const cars = await db
      .collection<CarDocument>(COLLECTIONS.CARS)
      .find({})
      .sort({ carName: 1 })
      .toArray()

    // Transform to API response format
    const carsResponse: CarResponse[] = cars.map(car => ({
      id: car._id!.toString(),
      carName: car.carName,
      weight: car.weight,
      frontBalance: car.frontBalance,
      cxRatio: car.cxRatio,
      frontDownforce: car.frontDownforce,
      rearDownforce: car.rearDownforce,
      sxRatio: car.sxRatio
    }))

    return NextResponse.json(carsResponse)
  } catch (error) {
    console.error('Error fetching cars:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cars' },
      { status: 500 }
    )
  }
}

// POST /api/cars - Create a new car
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { carName, weight, frontBalance, cxRatio, frontDownforce, rearDownforce, sxRatio, adminPassword } = body

    // Validate admin authentication
    const ACCESS_PASSWORD = process.env.ACCESS_PASSWORD
    if (!adminPassword || adminPassword !== ACCESS_PASSWORD) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid admin credentials' },
        { status: 401 }
      )
    }

    // Validate required fields
    if (!carName || !weight || !frontBalance || !cxRatio || !frontDownforce || !rearDownforce || !sxRatio) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate and parse numeric inputs
    const weightNum = Number(weight)
    const frontBalanceNum = Number(frontBalance)
    const frontDownforceNum = Number(frontDownforce)
    const rearDownforceNum = Number(rearDownforce)
    const sxRatioNum = Number(sxRatio)
    
    // Check for invalid numeric conversions
    if (isNaN(weightNum) || isNaN(frontBalanceNum) || isNaN(frontDownforceNum) || 
        isNaN(rearDownforceNum) || isNaN(sxRatioNum)) {
      return NextResponse.json(
        { error: 'Invalid numeric values provided' },
        { status: 400 }
      )
    }
    
    // Validate numeric ranges
    if (weightNum <= 0 || frontBalanceNum < 0 || frontBalanceNum > 100 ||
        frontDownforceNum < 0 || rearDownforceNum < 0 || sxRatioNum < 0) {
      return NextResponse.json(
        { error: 'Numeric values must be positive and within valid ranges' },
        { status: 400 }
      )
    }

    const db = await getDatabase()
    const newCar: Omit<CarDocument, '_id'> = {
      carName,
      weight: weightNum,
      frontBalance: frontBalanceNum,
      cxRatio: String(cxRatio),
      frontDownforce: frontDownforceNum,
      rearDownforce: rearDownforceNum,
      sxRatio: sxRatioNum,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const result = await db.collection<CarDocument>(COLLECTIONS.CARS).insertOne(newCar)

    const carResponse: CarResponse = {
      id: result.insertedId.toString(),
      carName: newCar.carName,
      weight: newCar.weight,
      frontBalance: newCar.frontBalance,
      cxRatio: newCar.cxRatio,
      frontDownforce: newCar.frontDownforce,
      rearDownforce: newCar.rearDownforce,
      sxRatio: newCar.sxRatio
    }

    return NextResponse.json(carResponse, { status: 201 })
  } catch (error) {
    console.error('Error creating car:', error)
    return NextResponse.json(
      { error: 'Failed to create car' },
      { status: 500 }
    )
  }
}
