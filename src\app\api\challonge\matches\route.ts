import { NextRequest, NextResponse } from 'next/server';
import { challongeAPI } from '@/lib/challonge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tournamentId = searchParams.get('tournamentId');

    if (!tournamentId) {
      return NextResponse.json(
        { error: 'Tournament ID is required' },
        { status: 400 }
      );
    }

    const matches = await challongeAPI.getMatches(tournamentId);
    return NextResponse.json({ matches });
  } catch (error) {
    console.error('Error fetching matches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch matches' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { tournamentId, matchId, winner_id, scores_csv, player1_votes, player2_votes } = body;

    if (!tournamentId || !matchId) {
      return NextResponse.json(
        { error: 'Tournament ID and match ID are required' },
        { status: 400 }
      );
    }

    const match = await challongeAPI.updateMatch(tournamentId, matchId, {
      winner_id,
      scores_csv,
      player1_votes,
      player2_votes
    });

    return NextResponse.json({ match });
  } catch (error) {
    console.error('Error updating match:', error);
    return NextResponse.json(
      { error: 'Failed to update match' },
      { status: 500 }
    );
  }
}