{"name": "envycarx", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "migrate": "tsx scripts/migrate-data.ts"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.0.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.312.0", "mongodb": "^6.16.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.11.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "20.17.12", "@types/react": "18.3.18", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "dotenv": "^16.5.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-testing-library": "^7.2.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "5.7.3"}}