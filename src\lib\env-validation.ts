/**
 * Environment variable validation utility
 * This ensures all required environment variables are present
 */

export function validateEnvironmentVariables() {
  const requiredEnvVars = {
    MONGODB_URI: process.env.MONGODB_URI,
    ACCESS_PASSWORD: process.env.ACCESS_PASSWORD,
  }

  const missingVars: string[] = []

  for (const [key, value] of Object.entries(requiredEnvVars)) {
    if (!value) {
      missingVars.push(key)
    }
  }

  if (missingVars.length > 0) {
    const error = `Missing required environment variables: ${missingVars.join(', ')}`
    throw new Error(error)
  }

  return true
}
