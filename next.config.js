/** @type {import('next').NextConfig} */
const nextConfig = {
  // Ensure API routes work properly on Netlify
  experimental: {
    serverComponentsExternalPackages: ['mongodb']
  },

  // Add headers for better API performance
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
